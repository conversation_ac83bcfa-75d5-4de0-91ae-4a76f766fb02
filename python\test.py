import json
import os
import signal
import pickle
import time
import traceback
import pandas as pd
import numpy as np
import re
import jieba
import faiss
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from tqdm import tqdm
from openai import OpenAI
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.neighbors import NearestNeighbors
from scipy.sparse import csr_matrix
from scipy.sparse.csgraph import minimum_spanning_tree, connected_components
from sklearn.cluster import MiniBatchKMeans, DBSCAN, KMeans, AgglomerativeClustering
from sklearn.metrics import calinski_harabasz_score, silhouette_score

# --- Configuration ---
DASHSCOPE_API_KEY = "sk-20010909"
DASHSCOPE_BASE_URL = "https://sf.yifanzhang2001.xyz/v1"

# 模型配置
EMBEDDING_MODEL_OPENAI = "Qwen/Qwen3-Embedding-8B"
LLM_MODEL_FOR_FILTERING = "deepseek-ai/DeepSeek-V3"
LLM_MODEL_FOR_INTERPRETATION = "deepseek-ai/DeepSeek-V3"

# 文件路径
INPUT_CSV_FILE = r"2023_c.csv"
TEXT_COLUMN = "text"

STOPWORDS_PATH = 'stopwords.txt'
OUTPUT_RESULTS_CSV_PATH = 'tsf_analysis_results.csv'
OUTPUT_TSF_INTERPRETATIONS_CSV_PATH = 'tsf_interpretations.csv'

# 新增：步骤4进度保存配置
STEP4_PROGRESS_DIR = 'step4_progress'
STEP4_INPUT_CACHE = os.path.join(STEP4_PROGRESS_DIR, 'input_sentences.pkl')
STEP4_RESULTS_CACHE = os.path.join(STEP4_PROGRESS_DIR, 'llm_results.json')
STEP4_PROGRESS_FILE = os.path.join(STEP4_PROGRESS_DIR, 'progress_info.json')
# 新增：保存所有已处理ID的文件
STEP4_PROCESSED_IDS_FILE = os.path.join(STEP4_PROGRESS_DIR, 'llm_processed_ids.json')

# 新增：步骤3.5中间结果保存配置
STEP35_SAVE_DIR = 'step35_keyword_results'
STEP35_SENTENCES_FILE = os.path.join(STEP35_SAVE_DIR, 'screened_sentences_keywords.pkl')
STEP35_INFO_FILE = os.path.join(STEP35_SAVE_DIR, 'keyword_screening_info.json')

# 全局中断标志
_interrupt_requested = False
_force_stop = False

def signal_handler(signum, frame):
    """信号处理器，用于优雅中断"""
    global _interrupt_requested
    print("\n收到中断信号，正在安全停止...")
    _interrupt_requested = True

# 注册信号处理器（但在交互式环境中可能不起作用）
try:
    signal.signal(signal.SIGINT, signal_handler)
except:
    print("注意：无法注册信号处理器（在某些交互式环境中正常）")

# 新增：交互式环境友好的中断检测函数
def check_interrupt_interactive():
    """
    在交互式环境中检测中断的函数
    支持多种中断检测方式
    """
    global _interrupt_requested, _force_stop
    
    # 方式1：检查全局标志
    if _interrupt_requested or _force_stop:
        return True
    
    # 方式2：尝试检测KeyboardInterrupt
    try:
        import sys
        if hasattr(sys, 'ps1'):  # 检测是否在交互式环境
            # 在交互式环境中使用非阻塞检查
            import select
            import tty
            import termios
            # 这种方法在某些环境中可能不适用，所以包装在try中
            pass
    except:
        pass
    
    return False

def force_stop_processing():
    """
    强制停止处理的函数
    用户可以在另一个代码单元格中调用此函数来强制停止
    """
    global _force_stop, _interrupt_requested
    _force_stop = True
    _interrupt_requested = True
    print("💥 强制停止标志已设置！处理将在下一次检查时停止。")
    print("💡 提示：如果仍然没有停止，请重启Python内核。")

# --- 步骤3.5中间结果管理函数 ---
def save_step35_intermediate_results(screened_sentences_keywords, info):
    """
    保存步骤3.5的中间结果到文件
    """
    try:
        # 创建保存目录
        if not os.path.exists(STEP35_SAVE_DIR):
            os.makedirs(STEP35_SAVE_DIR)
        
        # 保存筛选后的句子数据
        with open(STEP35_SENTENCES_FILE, 'wb') as f:
            pickle.dump(screened_sentences_keywords, f)
        
        # 保存信息文件
        with open(STEP35_INFO_FILE, 'w', encoding='utf-8') as f:
            json.dump(info, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 中间结果已保存到目录: {STEP35_SAVE_DIR}")
        print(f"  - 句子数据: {STEP35_SENTENCES_FILE}")
        print(f"  - 信息文件: {STEP35_INFO_FILE}")
        
    except Exception as e:
        print(f"保存中间结果失败: {e}")

def load_step35_intermediate_results():
    """
    加载步骤3.5保存的中间结果
    返回: (screened_sentences_keywords, info)
    """
    try:
        # 加载句子数据
        if not os.path.exists(STEP35_SENTENCES_FILE):
            print(f"句子数据文件不存在: {STEP35_SENTENCES_FILE}")
            return None, None
        
        with open(STEP35_SENTENCES_FILE, 'rb') as f:
            screened_sentences_keywords = pickle.load(f)
        
        # 加载信息文件
        info = {}
        if os.path.exists(STEP35_INFO_FILE):
            with open(STEP35_INFO_FILE, 'r', encoding='utf-8') as f:
                info = json.load(f)
        
        print(f"✓ 成功加载中间结果: {len(screened_sentences_keywords)} 个句子")
        return screened_sentences_keywords, info
        
    except Exception as e:
        print(f"加载中间结果失败: {e}")
        return None, None

def clean_step35_intermediate_results():
    """
    清理步骤3.5的中间结果文件
    """
    try:
        if os.path.exists(STEP35_SAVE_DIR):
            shutil.rmtree(STEP35_SAVE_DIR)
            print(f"✓ 已清理中间结果目录: {STEP35_SAVE_DIR}")
    except Exception as e:
        print(f"清理中间结果失败: {e}")

def step35_direct_load_and_continue():
    """
    直接加载步骤3.5的结果并继续后续流程
    适用于想跳过前面步骤，直接从LLM筛选开始的情况
    """
    print("=== 直接从步骤3.5结果开始 ===")
    
    # 检查是否存在保存的结果
    if not os.path.exists(STEP35_INFO_FILE):
        print("❌ 未找到步骤3.5的保存结果")
        print("请先运行完整流程或确保已保存步骤3.5的结果")
        return None
    
    # 加载结果
    screened_sentences_keywords, info = load_step35_intermediate_results()
    
    if screened_sentences_keywords is None:
        print("❌ 加载步骤3.5结果失败")
        return None
    
    print(f"✓ 成功加载步骤3.5结果")
    print(f"  保存时间: {info.get('timestamp', '未知')}")
    print(f"  句子数量: {len(screened_sentences_keywords)}")
    
    # 询问是否继续
    user_choice = input("\n是否继续执行后续流程 (步骤4-10)？(y/n): ").strip().lower()
    if user_choice not in ['y', 'yes']:
        print("用户取消继续执行")
        return screened_sentences_keywords
    
    # 继续执行后续流程
    print("\n开始执行后续流程...")
    
    # 需要初始化客户端
    dashscope_client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=DASHSCOPE_BASE_URL)
    
    # 步骤4: LLM筛选
    screened_sentences_llm = step4_llm_screening(screened_sentences_keywords, dashscope_client)
    
    # 步骤5: 生成向量
    final_filtered_sentences_texts, embeddings_filtered = step5_generate_embeddings(
        screened_sentences_llm, dashscope_client
    )
    
    # 步骤6: 聚类
    cluster_labels = step6_clustering(embeddings_filtered)
    
    # 步骤7: TSFs构建
    tsf_core_vectors, tsf_loadings_data = step7_build_tsfs(embeddings_filtered, cluster_labels)
    
    # 步骤8: TSF解释
    tsf_interpretations_list = step8_interpret_tsfs(
        tsf_core_vectors, cluster_labels, screened_sentences_llm, 
        embeddings_filtered, dashscope_client
    )
    
    # 步骤9: 计算企业级TSF得分
    df_company_scores = step9_calculate_company_tsf_scores(
        screened_sentences_llm, embeddings_filtered, tsf_core_vectors, cluster_labels
    )
    
    # 步骤10: 保存所有结果
    df_results, df_interpretations, df_company_scores = step10_save_all_results(
        screened_sentences_llm, cluster_labels, tsf_loadings_data, tsf_interpretations_list, df_company_scores
    )
    
    print("=" * 50)
    print("完整流程执行完毕！")
    print("=" * 50)
    
    return {
        'screened_sentences_keywords': screened_sentences_keywords,
        'screened_sentences_llm': screened_sentences_llm,
        'embeddings_filtered': embeddings_filtered,
        'cluster_labels': cluster_labels,
        'tsf_core_vectors': tsf_core_vectors,
        'tsf_loadings_data': tsf_loadings_data,
        'tsf_interpretations_list': tsf_interpretations_list,
        'df_results': df_results,
        'df_interpretations': df_interpretations,
        'df_company_scores': df_company_scores
    }

# --- 相关关键词词典
KEYWORDS_DICT = set([
    "研发", "开发", "创新", "研究", "升级", "转型", "改革", "新能源", "专利",
    "新型", "创造", "新材料", "新技术", "革新", "高新技术", "知识产权", "创制", "变革",
    "新品", "新一代", "更新", "新工艺", "全新", "产学研", "新模式", "发明", "迭代",
    "革新", "新业态", "原创", "首创", "革命", "推陈出新", "科技攻关", "新途径", "独创",
    "产学", "产研", "校企", "产业链", "创新链", "供应链", "研究院", "研究所"
])


# --- Utility Functions ---
def load_stopwords(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return set(line.strip() for line in f)
    except FileNotFoundError:
        print(f"警告: 停用词文件 {filepath} 未找到。将使用空停用词列表。")
        return set()

STOPWORDS = load_stopwords(STOPWORDS_PATH)

# 初始化jieba词典
for word in KEYWORDS_DICT:
    jieba.add_word(word)

def preprocess_text_for_keywords(text):
    if not isinstance(text, str) or text is None:
        return ""
    text = text.lower()
    return text

def preprocess_text_for_llm_embedding(text):
    if not isinstance(text, str) or text is None:
        return ""
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def split_into_sentences(text):
    if not isinstance(text, str) or text is None:
        return []
    sentences = re.split(r'(?<=[。！？?!;；])\s*', text)
    return [s.strip() for s in sentences if s.strip()]

# 添加缺失的call_llm函数
def call_llm(prompt_text, model_name, client, retries=3, temperature=1, max_tokens=150, top_p=1):
    """
    LLM调用函数（兼容性版本）
    """
    return call_llm_with_retry(prompt_text, model_name, client, retries, temperature, max_tokens, top_p, delay=0.1)

# 优化并发控制参数
_api_call_lock = Lock()
_api_call_count = 0
_rate_limit_delay = 0.02  # 减少基础延迟以支持更高并发

def call_llm_with_retry(prompt_text, model_name, client, retries=3, temperature=0.2, max_tokens=150, top_p=0.7, delay=0.02):
    """
    带重试和速率限制的LLM调用函数 - 优化版本支持高并发
    """
    global _api_call_count
    
    for attempt in range(retries):
        try:
            # 优化的速率限制 - 支持更高并发
            with _api_call_lock:
                _api_call_count += 1
                # 每50次调用暂停一下，减少暂停频率
                if _api_call_count % 50 == 0:
                    time.sleep(0.05)
            
            completion = client.chat.completions.create(
                model=model_name,
                messages=[{'role': 'user', 'content': prompt_text}],
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p
            )
            response_content = completion.choices[0].message.content.strip()
            
            # 减少延迟以支持高并发
            time.sleep(delay)
            return response_content
            
        except Exception as e:
            print(f"LLM API 调用错误 (尝试 {attempt + 1}/{retries}) for model {model_name}: {e}")
            if attempt < retries - 1:
                time.sleep(min(2 ** attempt, 5))  # 限制最大退避时间
            else:
                print(f"LLM API 调用最终失败: {prompt_text[:100]}...")
                return None

def process_single_sentence_llm(args):
    """
    处理单个句子的LLM筛选与提取
    """
    sent_id, sentence, company_id, year, llm_client, llm_model = args
    
    combined_prompt_template = (
        "企业的“产业链与创新链融合”是指：企业在产业链对科技创新需求的驱动下,将资源部署在促进产业技术进步领域,根据产业发展需求布局科技创新前沿,尤其是聚焦产业化前景明朗的前沿科技以及可满足当前产业需求的先进技术,带动创新链加速转化,加快实现双链融合” "
        "同时企业在创新链对技术转化需求的驱动下,产业化运营培育全新的产业领域以致推动产业链升级\n\n"
        "请基于这一概念思想对以下句子进行两步处理，严格遵循我的输出要求，不要输出任何的其余信息：\n\n"
        "步骤1：判断句子是否具体描述或体现了企业进行了“产业链与创新链融合”，体现了企业在“产业链与创新链融合”上的相关做法、"
        "模式、路径。"
        "请排除仅为一般性提及、展望、可能性表述、或与“产业链与创新链融合”不直接相关的句子。\n\n"
        "步骤2：如果句子不符合要求，请直接回答'无关联信息'。注意不要输出任何其他信息"
        "如果句子符合要求，请进行深层语义分析和提取（仅仅输出提取后的精简的句子）：\n"
        "- 识别句子中与“产业链与创新链融合”做法、模式或路径相关的核心信息\n"
        "- 提取并重构句子，使其仅包含明确描述“产业链与创新链融合”的具体做法、模式的部分，体现企业进行双链融合的做法、模式、路径\n"
        "- 剔除模糊表述、可能性描述等无关信息;剔除具体涉及的产业或者技术细节（例如涉及具体的产品、技术等信息都剔除），仅保留企业做法维度上双链融合的做法、模式、路径部分，并且注意提炼的句子要严格按着原文句子的含义，不要曲解原句意思或者进行过度延伸。\n"
        "- 输出提取后的精炼句子，不要输出任何其他内容\n\n"
        "原句子：'{sentence}'\n"
        "处理结果："
    )
    
    prompt = combined_prompt_template.format(
        sentence=preprocess_text_for_llm_embedding(sentence)
    )
    
    response = call_llm_with_retry(prompt, llm_model, llm_client, max_tokens=500, temperature=0.1, delay=0.1)
    
    if response and '无关联信息' not in response and len(response.strip()) > 10:
        # 清理和验证提取的句子
        extracted_sentence = response.strip()
        # 移除可能的引号或其他格式标记
        extracted_sentence = re.sub(r'^["\']|["\']$', '', extracted_sentence)
        
        if extracted_sentence and len(extracted_sentence) > 5:  # 确保提取的句子有意义
            return (sent_id, extracted_sentence, company_id, year)
    
    return None

def llm_semantic_screening_and_extraction(sentences_with_ids, llm_client, llm_model, max_workers=200):
    """
    基于LLM的语义筛选与增强提取一体化处理 - 支持断点续传、自动保存、交互式中断友好
    """
    import threading
    import tempfile
    import shutil
    global _interrupt_requested
    print(f"开始LLM语义筛选与增强提取一体化处理 (高并发模式，{max_workers} 个工作线程，支持断点续传)...")
    if not sentences_with_ids:
        return []
    # 1. 创建进度目录
    if not os.path.exists(STEP4_PROGRESS_DIR):
        os.makedirs(STEP4_PROGRESS_DIR)
    # 2. 检查是否有之前的进度
    completed_results = []
    processed_ids = set()
    # 新增：恢复已处理ID集合
    if os.path.exists(STEP4_PROCESSED_IDS_FILE):
        try:
            with open(STEP4_PROCESSED_IDS_FILE, 'r', encoding='utf-8') as f:
                processed_ids = set(json.load(f))
            print(f"恢复了 {len(processed_ids)} 个已处理ID")
        except Exception as e:
            print(f"恢复已处理ID时出错: {e}，将重新开始")
            processed_ids = set()
    if os.path.exists(STEP4_RESULTS_CACHE):
        try:
            with open(STEP4_RESULTS_CACHE, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)
                if isinstance(cached_data, list):
                    completed_results = [
                        (item['id'], item['sentence'], item['company_id'], item['year'])
                        for item in cached_data
                    ]
                    # 只把通过筛选的ID加入processed_ids
                    processed_ids.update(item['id'] for item in cached_data)
                    print(f"恢复了 {len(completed_results)} 个已完成的结果")
        except Exception as e:
            print(f"恢复进度时出错: {e}，将重新开始")
            completed_results = []
    # 3. 过滤出还需要处理的任务
    remaining_tasks = [task for task in sentences_with_ids if task[0] not in processed_ids]
    print(f"还需要处理 {len(remaining_tasks)} 个任务（总任务数: {len(sentences_with_ids)}）")
    if not remaining_tasks:
        print("所有任务都已完成，直接返回结果")
        return completed_results
    # 4. 保存当前输入到缓存文件
    try:
        with open(STEP4_INPUT_CACHE, 'wb') as f:
            pickle.dump(remaining_tasks, f)
    except Exception as e:
        print(f"保存输入缓存失败: {e}")
    # 5. 准备任务参数
    tasks = [(sent_id, sentence, company_id, year, llm_client, llm_model) for sent_id, sentence, company_id, year in remaining_tasks]
    extracted_sentences = completed_results.copy()
    completed_count = len(processed_ids)  # 新增：已处理总数
    error_count = 0
    save_every_n = 10  # 每N个保存
    save_every_sec = 30  # 每T秒保存
    last_save_time = time.time()
    save_lock = threading.Lock()
    # 新增：atomic_save_progress保存processed_ids
    def atomic_save_progress(data, processed_ids_set):
        try:
            with save_lock:
                tmpfile = STEP4_RESULTS_CACHE + '.tmp'
                results_data = [
                    {'id': r[0], 'sentence': r[1], 'company_id': r[2], 'year': r[3]}
                    for r in data
                ]
                with open(tmpfile, 'w', encoding='utf-8') as f:
                    json.dump(results_data, f, ensure_ascii=False, indent=2)
                shutil.move(tmpfile, STEP4_RESULTS_CACHE)
                # 保存已处理ID集合
                with open(STEP4_PROCESSED_IDS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(list(processed_ids_set), f, ensure_ascii=False, indent=2)
                progress_info = {
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_completed': len(processed_ids_set),
                    'status': 'in_progress'
                }
                with open(STEP4_PROGRESS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(progress_info, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存进度失败: {e}")
    # 6. 并发处理+自动保存+中断
    try:
        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="LLM-Worker") as executor:
            future_to_task = {executor.submit(process_single_sentence_llm, task): task for task in tasks}
            with tqdm(total=len(tasks), desc=f"LLM高并发处理({max_workers}线程)") as pbar:
                try:
                    for future in as_completed(future_to_task):
                        # 检查中断
                        if _interrupt_requested:
                            print("\n检测到中断请求，正在安全停止...")
                            for f in future_to_task:
                                if not f.done():
                                    f.cancel()
                            atomic_save_progress(extracted_sentences, processed_ids)
                            return extracted_sentences
                        # 检查定时保存
                        now = time.time()
                        if now - last_save_time > save_every_sec:
                            atomic_save_progress(extracted_sentences, processed_ids)
                            last_save_time = now
                        try:
                            result = future.result()
                            task = future_to_task[future]
                            sent_id = task[0]
                            processed_ids.add(sent_id)  # 新增：无论是否通过筛选都记录ID
                            completed_count += 1
                            if result is not None:
                                extracted_sentences.append(result)
                            pbar.update(1)
                            # 定量保存
                            if completed_count % save_every_n == 0:
                                atomic_save_progress(extracted_sentences, processed_ids)
                                last_save_time = time.time()
                            # 进度条信息
                            if completed_count % 100 == 0:
                                success_rate = (len(extracted_sentences) - len(completed_results)) / completed_count * 100
                                error_rate = error_count / completed_count * 100
                                pbar.set_postfix({
                                    'Success': f'{success_rate:.1f}%',
                                    'Error': f'{error_rate:.1f}%',
                                    'Total': len(extracted_sentences),
                                    'New': len(extracted_sentences) - len(completed_results),
                                    'Processed': completed_count
                                })
                        except KeyboardInterrupt:
                            print("\n收到交互式中断，正在保存进度...")
                            atomic_save_progress(extracted_sentences, processed_ids)
                            return extracted_sentences
                        except Exception as e:
                            print(f"处理任务时出错: {e}")
                            error_count += 1
                            completed_count += 1
                            pbar.update(1)
                finally:
                    pbar.close()
        atomic_save_progress(extracted_sentences, processed_ids)
        if not _interrupt_requested and completed_count == len(tasks) + len(processed_ids) - len(completed_results):
            _cleanup_progress_files()
            print("所有任务完成，已清理临时进度文件")
    except KeyboardInterrupt:
        print("\n收到键盘中断，正在保存当前进度...")
        atomic_save_progress(extracted_sentences, processed_ids)
        return extracted_sentences
    final_success_rate = len(extracted_sentences) / completed_count * 100 if completed_count else 0
    new_extractions = len(extracted_sentences) - len(completed_results)
    print(f"LLM语义筛选与提取完成")
    print(f"总共筛选出 {len(extracted_sentences)} 条精炼句子（本次新增: {new_extractions}）")
    print(f"总成功率: {final_success_rate:.1f}% (成功: {len(extracted_sentences)}, 已处理: {completed_count})")
    print(f"并发处理效果: 使用 {max_workers} 个线程")
    if _interrupt_requested:
        print(f"注意: 由于中断，还有 {len(sentences_with_ids) - completed_count} 个任务未完成")
        print("下次运行时将自动从中断点继续")
    return extracted_sentences

# --- Phase 1: 预处理模块 ---
## --- 关键词筛选 ---
def keyword_based_screening(sentences_with_ids, keywords, stopwords):
    """基于关键词的高效初筛"""
    print("开始关键词初筛...")
    screened_sentences = []
    for sent_id, sentence, company_id, year in tqdm(sentences_with_ids, desc="关键词筛选"):
        processed_sentence = preprocess_text_for_keywords(sentence)
        words = jieba.lcut(processed_sentence)
        filtered_words = []
        skip_next = False
        for word in words:
            if skip_next:
                skip_next = False
                continue
            if word not in stopwords and len(word) > 1:
                filtered_words.append(word)

        if filtered_words:
            found_keywords = []
            for word in filtered_words:
                if word in keywords:
                    found_keywords.append(word)
            
            if found_keywords:
                screened_sentences.append((sent_id, sentence, company_id, year))
    
    print(f"关键词初筛完成，筛选出 {len(screened_sentences)} 条句子。")
    return screened_sentences

# --- Phase 1.5: 关键词筛选与中间结果保存 ---
# 新增：步骤3.5 - 中间结果保存选择机制
def step35_save_intermediate_choice(screened_sentences_keywords):
    """
    步骤3.5: 询问用户是否保存关键词筛选后的中间结果
    """
    print("=== 步骤3.5: 中间结果保存选择 ===")
    
    if not screened_sentences_keywords:
        print("关键词筛选结果为空，无需保存")
        return False
    
    print(f"关键词筛选完成，共筛选出 {len(screened_sentences_keywords)} 个句子")
    
    # 询问用户是否保存中间结果
    while True:
        user_choice = input("\n是否保存当前的关键词筛选结果？(y/n): ").strip().lower()
        if user_choice in ['y', 'yes']:
            # 保存中间结果
            info = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': len(screened_sentences_keywords),
                'filtered_count': len(screened_sentences_keywords),
                'step': 'keyword_screening_completed',
                'description': '关键词筛选完成，可从此步骤继续执行后续流程'
            }
            
            save_step35_intermediate_results(screened_sentences_keywords, info)
            print("✓ 中间结果已保存，下次可直接从LLM筛选步骤开始")
            return True
            
        elif user_choice in ['n', 'no']:
            print("✓ 用户选择不保存，将直接继续执行")
            return False
        else:
            print("请输入 y 或 n")


## --- LLM语义筛选与提取一体化（基于大模型一步完成判断和精炼） ---
# 这里删除从第271行到第310行的重复函数定义

# --- Phase 2: 句向量嵌入 ---
def get_openai_embeddings(sentences_texts, client, model=EMBEDDING_MODEL_OPENAI, max_workers=10):
    print(f"开始使用OpenAI模型 {model} 生成句向量（{max_workers}线程并发）...")
    embeddings = []
    batch_size = 10  # 遵循API最大批大小限制
    batches = [sentences_texts[i:i+batch_size] for i in range(0, len(sentences_texts), batch_size)]
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    def fetch_embedding(batch):
        """带重试机制的嵌入获取函数"""
        max_retries = 3
        retry_delay = 1  # 1秒重试延迟
        
        for attempt in range(max_retries):
            try:
                response = client.embeddings.create(
                    model=model,
                    input=batch,
                    encoding_format="float"
                )
                return [item.embedding for item in response.data]
            except Exception as e:
                print(f"生成句向量时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    print(f"批次处理最终失败，跳过该批次")
                    return [None] * len(batch)
        
        return [None] * len(batch)
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_batch = {executor.submit(fetch_embedding, batch): batch for batch in batches}
        for future in tqdm(as_completed(future_to_batch), total=len(batches), desc="生成句向量"):
            result = future.result()
            embeddings.extend(result)
    
    # 过滤掉None值
    valid_embeddings = [emb for emb in embeddings if emb is not None]
    failed_count = len(embeddings) - len(valid_embeddings)
    
    if failed_count > 0:
        print(f"警告: {failed_count} 个嵌入向量生成失败")
    
    if not valid_embeddings:
        raise RuntimeError("所有嵌入向量生成都失败了，请检查API配置和网络连接")
    
    return np.array(valid_embeddings)


# --- 步骤5.5: 句向量保存与加载机制 ---
EMBEDDINGS_SAVE_PATH = 'embeddings_filtered.npz'
EMBEDDINGS_TEXTS_SAVE_PATH = 'final_filtered_sentences_texts.json'

def save_embeddings_and_texts(embeddings, texts_with_ids):
    """
    保存句向量和文本信息到本地文件
    """
    try:
        np.savez(EMBEDDINGS_SAVE_PATH, embeddings=embeddings)
        with open(EMBEDDINGS_TEXTS_SAVE_PATH, 'w', encoding='utf-8') as f:
            json.dump(texts_with_ids, f, ensure_ascii=False, indent=2)
        print(f"✓ 向量和文本已保存到: {EMBEDDINGS_SAVE_PATH}, {EMBEDDINGS_TEXTS_SAVE_PATH}")
    except Exception as e:
        print(f"保存向量或文本失败: {e}")

def load_embeddings_and_texts():
    """
    加载本地保存的句向量和文本信息
    返回: (final_filtered_sentences_texts, embeddings_filtered, screened_sentences_llm)
    """
    try:
        data = np.load(EMBEDDINGS_SAVE_PATH)
        embeddings_filtered = data['embeddings']
        with open(EMBEDDINGS_TEXTS_SAVE_PATH, 'r', encoding='utf-8') as f:
            texts_with_ids = json.load(f)
        # texts_with_ids 兼容两种格式：仅文本或4元组
        if texts_with_ids and isinstance(texts_with_ids[0], (list, tuple)) and len(texts_with_ids[0]) == 4:
            screened_sentences_llm = texts_with_ids
            final_filtered_sentences_texts = [x[1] for x in texts_with_ids]
        else:
            # 兼容仅文本
            screened_sentences_llm = None
            final_filtered_sentences_texts = texts_with_ids
        print(f"✓ 已加载本地保存的向量和文本: {embeddings_filtered.shape}, {len(final_filtered_sentences_texts)} 条")
        return final_filtered_sentences_texts, embeddings_filtered, screened_sentences_llm
    except Exception as e:
        print(f"加载向量或文本失败: {e}")
        return None, None, None

# --- Phase 3: S-DCESCD 可扩展密度与连接增强语义社区发现 ---

class SDCESCDClustering:
    """
    S-DCESCD: 可扩展密度与连接增强语义社区发现
    严格按照三步骤创新思路实现
    """
    
    def __init__(self, min_cluster_size=2, k_neighbors=5, eps_percentile=0.2, 
                 similarity_threshold=0.7, merge_threshold=0.85):
        self.min_cluster_size = min_cluster_size
        self.k_neighbors = k_neighbors
        self.eps_percentile = eps_percentile  # 用于动态确定密度阈值
        self.similarity_threshold = similarity_threshold
        self.merge_threshold = merge_threshold
    
    def fit_predict(self, embeddings):
        """主要聚类函数"""
        print("开始S-DCESCD聚类...")
        n_samples = embeddings.shape[0]
        
        if n_samples < self.min_cluster_size:
            print(f"样本数量 {n_samples} 小于最小聚类大小 {self.min_cluster_size}，将所有样本归为一个簇")
            return np.zeros(n_samples, dtype=int)
        
        # 步骤一：基于ANN的全局候选区域划分
        candidate_regions = self._step1_ann_region_partitioning(embeddings)
        
        # 步骤二：区域内精细化DCESCD
        local_communities = self._step2_local_dcescd(embeddings, candidate_regions)
        
        # 步骤三：全局社区整合
        final_labels = self._step3_global_integration(embeddings, local_communities)
        
        print(f"S-DCESCD聚类完成，发现 {len(set(final_labels))} 个社区")
        return final_labels
    
    def _step1_ann_region_partitioning(self, embeddings):
        """
        步骤一：基于近似最近邻(ANN)的全局候选区域划分与索引构建
        严格使用LSH分桶思想，而非K-means聚类
        """
        print("步骤一：基于LSH的ANN候选区域划分...")
        n_samples = embeddings.shape[0]
        
        # 根据数据量确定候选区域数量
        if n_samples <= 10:
            n_regions = min(2, n_samples // 2)
        elif n_samples <= 50:
            n_regions = min(3, n_samples // 5)
        else:
            n_regions = min(8, max(3, n_samples // 15))  # 增加区域数以更好体现LSH分桶
        
        print(f"将 {n_samples} 个样本通过LSH划分为 {n_regions} 个候选区域")
        
        try:
            # 方法一：使用FAISS的LSH索引进行分桶
            d = embeddings.shape[1]
            # 归一化嵌入以适配LSH
            norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
            emb_norm = embeddings / (norms + 1e-6)
            
            # 使用FAISS的LSH索引（基于随机投影）
            nbits = min(64, max(16, d // 4))  # 动态确定hash位数
            index_lsh = faiss.IndexLSH(d, nbits)
            index_lsh.add(emb_norm.astype('float32'))
            
            # 构建ANN索引用于查询
            index_flat = faiss.IndexFlatIP(d)
            index_flat.add(emb_norm.astype('float32'))
            
            # 基于LSH的分桶策略：为每个点找到其最近邻，然后基于邻域密度分桶
            k_for_bucketing = min(10, n_samples // 2)
            _, neighbor_indices = index_flat.search(emb_norm.astype('float32'), k_for_bucketing)
            
            # 计算每个点的局部密度（邻域内的平均相似度）
            local_densities = []
            for i in range(n_samples):
                neighbors = neighbor_indices[i, 1:]  # 排除自身
                neighbor_sims = np.dot(emb_norm[i], emb_norm[neighbors].T)
                local_densities.append(np.mean(neighbor_sims))
            
            local_densities = np.array(local_densities)
            
            # 基于密度分位数进行分桶（类似LSH的多级哈希）
            density_thresholds = np.percentile(local_densities, np.linspace(0, 100, n_regions + 1))
            region_labels = np.digitize(local_densities, density_thresholds[1:-1])
            
            print(f"  LSH分桶完成，密度阈值: {density_thresholds}")
            
        except Exception as e:
            print(f"FAISS LSH分桶失败: {e}")
            # 方法二：简化的基于相似度的LSH分桶策略
            print("  使用简化LSH分桶策略...")
            
            # 选择种子点（高密度点作为桶的中心）
            similarities = cosine_similarity(embeddings)
            node_degrees = similarities.sum(axis=1)  # 节点度数作为密度指标
            
            # 选择n_regions个高度数点作为种子
            seed_indices = np.argsort(node_degrees)[-n_regions:]
            
            # 为每个样本分配到最相似的种子桶
            region_labels = np.zeros(n_samples, dtype=int)
            seed_embeddings = embeddings[seed_indices]
            
            for i in range(n_samples):
                sims_to_seeds = cosine_similarity(embeddings[i:i+1], seed_embeddings)[0]
                region_labels[i] = np.argmax(sims_to_seeds)
        
        # 组织候选区域，确保每个区域有足够样本
        candidate_regions = {}
        region_id = 0
        
        for orig_region_id in range(n_regions):
            indices = np.where(region_labels == orig_region_id)[0]
            
            if len(indices) >= self.min_cluster_size:
                candidate_regions[region_id] = {
                    'indices': indices,
                    'embeddings': embeddings[indices],
                    'size': len(indices),
                    'avg_density': local_densities[indices].mean() if 'local_densities' in locals() else 0
                }
                print(f"  候选区域 {region_id}: {len(indices)} 个样本 (平均密度: {candidate_regions[region_id]['avg_density']:.3f})")
                region_id += 1
            else:
                # 将小区域合并到最相似的大区域
                if candidate_regions:
                    # 找到最相似的已有区域
                    best_region = 0
                    best_sim = -1
                    
                    for existing_id, existing_region in candidate_regions.items():
                        sim = cosine_similarity(
                            embeddings[indices].mean(axis=0).reshape(1, -1),
                            existing_region['embeddings'].mean(axis=0).reshape(1, -1)
                        )[0, 0]
                        
                        if sim > best_sim:
                            best_sim = sim
                            best_region = existing_id
                    
                    # 合并到最相似区域
                    candidate_regions[best_region]['indices'] = np.concatenate([
                        candidate_regions[best_region]['indices'], indices
                    ])
                    candidate_regions[best_region]['embeddings'] = embeddings[candidate_regions[best_region]['indices']]
                    candidate_regions[best_region]['size'] = len(candidate_regions[best_region]['indices'])
                    
                    print(f"  将小区域(样本数:{len(indices)})合并到区域 {best_region}")
        
        return candidate_regions
    
    def _step2_local_dcescd(self, embeddings, candidate_regions):
        """
        步骤二：在每个候选区域内部应用精细化DCESCD
        """
        print("步骤二：区域内精细化DCESCD...")
        local_communities = {}
        community_id = 0
        
        for region_id, region_data in candidate_regions.items():
            print(f"  处理候选区域 {region_id} (样本数: {region_data['size']})")
            
            # 构建局部密度增强语义图 (Local DESG)
            local_graph, densities = self._build_local_desg(region_data['embeddings'])
            
            # 基于局部DESG的社区发现
            region_communities = self._discover_communities_from_desg(
                local_graph, densities, region_data['indices']
            )
            
            # 记录局部社区
            for community in region_communities:
                if len(community) >= self.min_cluster_size:
                    local_communities[community_id] = {
                        'members': community,
                        'region_id': region_id,
                        'center': embeddings[community].mean(axis=0),
                        'density': densities[np.isin(region_data['indices'], community)].mean()
                    }
                    community_id += 1
        
        print(f"  发现 {len(local_communities)} 个局部社区")
        return local_communities
    
    def _build_local_desg(self, region_embeddings):
        """
        构建局部密度增强语义图 (Local DESG)
        严格按照HDBSCAN思想的图论转化实现
        """
        n_samples = region_embeddings.shape[0]
        
        if n_samples < self.k_neighbors + 1:
            k = max(1, n_samples - 1)
        else:
            k = self.k_neighbors
        
        print(f"    构建局部DESG，样本数: {n_samples}, k-近邻: {k}")
        
        try:
            # 使用 FAISS 进行高效 ANN k-近邻搜索
            norms = np.linalg.norm(region_embeddings, axis=1, keepdims=True)
            emb_norm = region_embeddings / (norms + 1e-6)
            d = emb_norm.shape[1]
            
            index = faiss.IndexFlatIP(d)
            index.add(emb_norm.astype('float32'))
            similarities, indices = index.search(emb_norm.astype('float32'), k+1)
            
            # 计算互达距离（核心密度距离）- HDBSCAN核心概念
            mutual_reachability_distances = np.zeros((n_samples, n_samples))
            
            # 1. 计算每个点的核心距离（k-距离）
            core_distances = np.zeros(n_samples)
            for i in range(n_samples):
                # k-近邻中最远点的距离作为核心距离
                k_dist = 1 - similarities[i, k]  # 转换为距离
                core_distances[i] = max(k_dist, 1e-10)  # 避免零值
            
            # 2. 构建基于互达距离的完全图
            for i in range(n_samples):
                for j in range(i+1, n_samples):
                    # 计算原始距离
                    cosine_sim = np.dot(emb_norm[i], emb_norm[j])
                    euclidean_dist = 1 - cosine_sim
                    
                    # 互达距离 = max(core_dist_i, core_dist_j, dist(i,j))
                    mutual_reach_dist = max(
                        core_distances[i], 
                        core_distances[j], 
                        euclidean_dist
                    )
                    
                    mutual_reachability_distances[i, j] = mutual_reach_dist
                    mutual_reachability_distances[j, i] = mutual_reach_dist
            
            # 3. 密度增强：基于核心密度调整权重
            densities = 1.0 / (core_distances + 1e-10)  # 密度 = 1/核心距离
            
            # 4. 构建密度增强的稀疏图（只保留k-近邻连接以提高效率）
            rows, cols, weights = [], [], []
            
            for i in range(n_samples):
                for j_idx in range(1, min(k+1, similarities.shape[1])):  # 排除自身
                    j = indices[i, j_idx]
                    if i != j and i < j:  # 避免重复边和自环
                        # 获取互达距离
                        mutual_dist = mutual_reachability_distances[i, j]
                        
                        # 转换为相似度权重（距离越小，权重越大）
                        base_weight = 1.0 / (mutual_dist + 1e-10)
                        
                        # 密度增强因子
                        density_factor = np.sqrt(densities[i] * densities[j])
                        
                        # 最终权重
                        enhanced_weight = base_weight * density_factor
                        
                        # 只保留超过阈值的边
                        if enhanced_weight > self.similarity_threshold * 0.3:
                            rows.extend([i, j])
                            cols.extend([j, i])
                            weights.extend([enhanced_weight, enhanced_weight])
            
            print(f"    DESG构建完成，边数: {len(weights)//2}, 平均密度: {densities.mean():.4f}")
            
        except Exception as e:
            print(f"    FAISS构建DESG失败，使用回退方案: {e}")
            # 回退到sklearn实现
            from sklearn.neighbors import NearestNeighbors
            nn = NearestNeighbors(n_neighbors=k+1, metric='cosine')
            nn.fit(region_embeddings)
            distances, indices = nn.kneighbors(region_embeddings)
            
            # 简化的密度估计
            densities = 1 - distances[:, 1:].mean(axis=1)
            
            rows, cols, weights = [], [], []
            for i in range(n_samples):
                for j_idx in range(1, k+1):
                    j = indices[i, j_idx]
                    if i != j:
                        sim = 1 - distances[i, j_idx]
                        density_factor = (densities[i] + densities[j]) / 2
                        weight = sim * density_factor
                        
                        if weight > self.similarity_threshold * 0.5:
                            rows.append(i)
                            cols.append(j)
                            weights.append(weight)
        
        # 构建稀疏图
        if len(weights) > 0:
            graph = csr_matrix((weights, (rows, cols)), shape=(n_samples, n_samples))
        else:
            # 如果没有有效边，创建空图
            graph = csr_matrix((n_samples, n_samples))
            
        return graph, densities
    
    def _discover_communities_from_desg(self, graph, densities, global_indices):
        """基于局部DESG的社区发现算法"""
        n_samples = graph.shape[0]
        
        if n_samples < self.min_cluster_size:
            return []
        
        # 使用改进的连通分量方法模拟HDBSCAN思想
        # 1. 构建最小生成树（MST）
        mst = minimum_spanning_tree(graph)
        
        # 2. 基于密度阈移除弱边
        mst_coo = mst.tocoo()
        density_threshold = np.percentile(densities, self.eps_percentile * 100)
        
        # 保留连接高密度节点的边
        valid_edges = []
        for i, j, weight in zip(mst_coo.row, mst_coo.col, mst_coo.data):
            if densities[i] > density_threshold and densities[j] > density_threshold:
                valid_edges.append((i, j, weight))
        
        # 3. 构建过滤后的图并找连通分量
        if valid_edges:
            filtered_rows = [edge[0] for edge in valid_edges]
            filtered_cols = [edge[1] for edge in valid_edges]
            filtered_weights = [edge[2] for edge in valid_edges]
            
            filtered_graph = csr_matrix(
                (filtered_weights, (filtered_rows, filtered_cols)), 
                shape=(n_samples, n_samples)
            )
            
            n_components, component_labels = connected_components(
                csgraph=filtered_graph, directed=False
            )
        else:
            # 如果没有有效边，每个高密度点自成一类
            component_labels = np.arange(n_samples)
            n_components = n_samples
        
        # 4. 过滤小社区并返回全局索引
        communities = []
        for component_id in range(n_components):
            component_members = np.where(component_labels == component_id)[0]
            
            # 只保留满足最小规模且平均密度足够的社区
            if len(component_members) >= self.min_cluster_size:
                avg_density = densities[component_members].mean()
                if avg_density > density_threshold:
                    # 转换为全局索引
                    global_members = global_indices[component_members]
                    communities.append(global_members)
        
        return communities
    
    def _step3_global_integration(self, embeddings, local_communities):
        """
        步骤三：全局语义社区的整合与后处理
        """
        print("步骤三：全局社区整合...")
        n_samples = embeddings.shape[0]
        final_labels = -1 * np.ones(n_samples, dtype=int)
        
        if not local_communities:
            print("  没有发现有效的局部社区，所有样本归为噪声")
            return final_labels
        
        # 1. 社区代表向量生成
        community_centers = []
        community_ids = list(local_communities.keys())
        
        for comm_id in community_ids:
            center = local_communities[comm_id]['center']
            community_centers.append(center)
        
        community_centers = np.array(community_centers)
        
        # 2. 高层聚类/匹配：基于代表向量的相似度合并社区
        if len(community_centers) > 1:
            center_similarity = cosine_similarity(community_centers)
            
            # 合并相似的社区
            merged_groups = self._merge_similar_communities(
                center_similarity, self.merge_threshold
            )
        else:
            merged_groups = [[0]]
        
        # 3. 分配最终标签
        final_community_id = 0
        
        for group in merged_groups:
            # 为每个合并组分配相同的标签
            for local_comm_idx in group:
                comm_id = community_ids[local_comm_idx]
                members = local_communities[comm_id]['members']
                final_labels[members] = final_community_id
            final_community_id += 1
        
        # 4. 噪声点再分配
        noise_indices = np.where(final_labels == -1)[0]
        if len(noise_indices) > 0 and final_community_id > 0:
            print(f"  对 {len(noise_indices)} 个噪声点进行再分配...")
            final_labels = self._reassign_noise_points(
                embeddings, final_labels, noise_indices, final_community_id
            )
        
        # 5. 兜底：如果所有点都是噪声，归为一个簇
        if np.all(final_labels == -1):
            final_labels[:] = 0
            print("  所有点都被识别为噪声，归为一个簇")
        
        return final_labels
    
    def _merge_similar_communities(self, similarity_matrix, threshold):
        """基于相似度阈值合并社区"""
        n_communities = similarity_matrix.shape[0]
        merged = np.full(n_communities, -1)
        groups = []
        current_group_id = 0
        
        for i in range(n_communities):
            if merged[i] != -1:
                continue
                
            # 创建新组
            current_group = [i]
            merged[i] = current_group_id
            
            # 寻找相似的社区
            for j in range(i + 1, n_communities):
                if merged[j] == -1 and similarity_matrix[i, j] > threshold:
                    current_group.append(j)
                    merged[j] = current_group_id
            
            groups.append(current_group)
            current_group_id += 1
        
        return groups
    
    def _reassign_noise_points(self, embeddings, labels, noise_indices, n_communities):
        """噪声点再分配"""
        # 计算每个社区的中心
        community_centers = []
        for comm_id in range(n_communities):
            community_members = np.where(labels == comm_id)[0]
            if len(community_members) > 0:
                center = embeddings[community_members].mean(axis=0)
                community_centers.append(center)
            else:
                community_centers.append(np.zeros(embeddings.shape[1]))
        
        community_centers = np.array(community_centers)
        
        # 为噪声点找到最相似的社区
        noise_embeddings = embeddings[noise_indices]
        similarities = cosine_similarity(noise_embeddings, community_centers)
        
        new_labels = labels.copy()
        for i, noise_idx in enumerate(noise_indices):
            best_community = np.argmax(similarities[i])
            max_similarity = similarities[i, best_community]
            
            # 只分配高相似度的噪声点
            if max_similarity > self.similarity_threshold:
                new_labels[noise_idx] = best_community
        
        return new_labels

def s_dcescd_clustering(embeddings, min_cluster_size=2, k_neighbors=3, 
                       eps_percentile=0.2, similarity_threshold=0.6, merge_threshold=0.8):
    """
    S-DCESCD聚类的便捷接口函数
    """
    clusterer = SDCESCDClustering(
        min_cluster_size=min_cluster_size,
        k_neighbors=k_neighbors,
        eps_percentile=eps_percentile,
        similarity_threshold=similarity_threshold,
        merge_threshold=merge_threshold
    )
    
    return clusterer.fit_predict(embeddings)



# --- Phase 4: TSFs 构建与量化 ---
def generate_tsf_core_vectors(embeddings, cluster_labels):
    """生成TSF核心向量"""
    print("开始生成TSF核心向量...")
    tsf_core_vectors = {}
    # 修改：只处理非噪声点的社区
    unique_labels = sorted(list(set(l for l in cluster_labels if l != -1)))

    for label in unique_labels:
        community_embeddings = embeddings[cluster_labels == label]
        if community_embeddings.shape[0] > 0:
            core_vector = np.mean(community_embeddings, axis=0)
            tsf_core_vectors[label] = core_vector
            print(f"  为社区 {label} 生成TSF (样本数: {community_embeddings.shape[0]})")
    
    print(f"TSF核心向量生成完成，共 {len(tsf_core_vectors)} 个TSFs。")
    return tsf_core_vectors

def calculate_tsf_loadings(sentence_embeddings, tsf_core_vectors):
    """计算句子在TSFs上的载荷"""
    print("开始计算TSF载荷...")
    loadings_data = []
    if not tsf_core_vectors:
        print("警告: 没有有效的TSF核心向量，无法计算载荷。")
        for i in range(sentence_embeddings.shape[0]):
            loadings_data.append({'sentence_idx_in_filtered_set': i})
        return loadings_data

    # 修改：只处理非噪声点的TSF
    tsf_ids = sorted(tsf_core_vectors.keys())
    
    for i, sent_emb in enumerate(tqdm(sentence_embeddings, desc="计算TSF载荷")):
        loading_dict = {'sentence_idx_in_filtered_set': i}
        for tsf_id in tsf_ids:
            core_vec = tsf_core_vectors[tsf_id]
            similarity = cosine_similarity(sent_emb.reshape(1, -1), core_vec.reshape(1, -1))[0, 0]
            loading_dict[f'TSF_{tsf_id}_loading'] = similarity
        loadings_data.append(loading_dict)
    
    # 新增：0-1标准化步骤
    print("开始对TSF载荷进行0-1标准化...")
    if loadings_data and tsf_ids:
        # 收集每个TSF维度的所有载荷值
        for tsf_id in tsf_ids:
            tsf_column = f'TSF_{tsf_id}_loading'
            # 提取该TSF的所有载荷值
            loadings_values = [loading_dict[tsf_column] for loading_dict in loadings_data if tsf_column in loading_dict]
            
            if loadings_values:
                loadings_array = np.array(loadings_values)
                min_val = np.min(loadings_array)
                max_val = np.max(loadings_array)
                
                # 避免除零错误
                if max_val - min_val > 1e-10:
                    # 进行0-1标准化并更新数据
                    for loading_dict in loadings_data:
                        if tsf_column in loading_dict:
                            original_val = loading_dict[tsf_column]
                            normalized_val = (original_val - min_val) / (max_val - min_val)
                            loading_dict[tsf_column] = normalized_val
                    
                    print(f"  TSF_{tsf_id}: 原始范围[{min_val:.4f}, {max_val:.4f}] -> 标准化范围[0, 1]")
                else:
                    print(f"  TSF_{tsf_id}: 载荷值相同({min_val:.4f})，跳过标准化")
    
    print("TSF载荷计算和0-1标准化完成。")
    return loadings_data

# --- Phase 5: LLM辅助的TSF解释 ---
def get_representative_sentences_for_tsf(tsf_id, cluster_labels, sentences_with_ids, embeddings, tsf_core_vector, top_n=20):
    """为TSF获取代表性句子，确保获取足够的代表性样本"""
    # 修改：只处理非噪声点的社区
    community_indices = np.where(cluster_labels == tsf_id)[0]
        
    if len(community_indices) == 0:
        return []
    
    community_embeddings = embeddings[community_indices]
    community_sent_data = [sentences_with_ids[i] for i in community_indices]

    similarities = cosine_similarity(community_embeddings, tsf_core_vector.reshape(1, -1)).flatten()
    sorted_indices = np.argsort(similarities)[::-1]
    
    # 修改：如果簇中句子数量少于top_n，则使用全部句子
    actual_top_n = min(top_n, len(sorted_indices))
    
    representative_sentences = []
    for i in range(actual_top_n):
        original_idx_in_community = sorted_indices[i]
        sentence_text = community_sent_data[original_idx_in_community][1]
        representative_sentences.append(sentence_text)
        
    return representative_sentences

def get_all_clusters_representative_sentences(tsf_core_vectors, cluster_labels, sentences_with_ids_filtered, embeddings_filtered, top_n_per_cluster=3):
    """获取所有非噪声簇的代表性句子，用于LLM对比分析"""
    all_clusters_sentences = {}
    
    for tsf_id, core_vector in tsf_core_vectors.items():
        # 明确跳过噪声簇
        if tsf_id == -1:
            print(f"跳过噪声簇 (TSF_ID: {tsf_id}) 的代表性句子获取")
            continue
            
        rep_sentences = get_representative_sentences_for_tsf(
            tsf_id, cluster_labels, sentences_with_ids_filtered, embeddings_filtered,
            core_vector, top_n=top_n_per_cluster
        )
        if rep_sentences:
            all_clusters_sentences[tsf_id] = rep_sentences
    
    return all_clusters_sentences

def interpret_tsfs_with_llm(tsf_core_vectors, cluster_labels, sentences_with_ids_filtered, embeddings_filtered, llm_client, llm_model):
    """使用LLM为TSFs生成标签和描述，考虑簇间区别性，但噪声簇直接标记为"噪声"""
    print("开始使用LLM解释TSFs...")
    interpretations = []
    
    # 检查是否有噪声簇（标签为-1）
    has_noise_cluster = -1 in cluster_labels
    noise_count = np.sum(cluster_labels == -1) if has_noise_cluster else 0
    
    if has_noise_cluster:
        print(f"发现噪声簇，包含 {noise_count} 个样本，将直接标记为'噪声'")
        # 为噪声簇添加解释，不经过LLM
        interpretations.append({
            'tsf_id': -1,
            'label': '噪声',
            'description': '聚类算法识别的噪声数据，不构成明确的主题模式。',
            'representative_sentences_for_llm': [],
            'num_representative_sentences': 0
        })
    
    # 首先获取所有非噪声簇的代表性句子用于对比
    all_clusters_sentences = get_all_clusters_representative_sentences(
        tsf_core_vectors, cluster_labels, sentences_with_ids_filtered, embeddings_filtered, top_n_per_cluster=3
    )
    
    # 改进的标签生成提示模板
    label_prompt_template = (
        "企业的“产业链与创新链融合”是指：企业在产业链对科技创新需求的驱动下,将资源部署在促进产业技术进步领域,根据产业发展需求布局科技创新前沿,尤其是聚焦产业化前景明朗的前沿科技以及可满足当前产业需求的先进技术,带动创新链加速转化,加快实现双链融合” "
        "同时企业在创新链对技术转化需求的驱动下,产业化运营培育全新的产业领域以致推动产业链升级\n\n"
        "以下是一组围绕“产业链与创新链融合”企业做法、模式、路径相关核心主题的句子，它们共同构成了一个'文本结构因子',反映了企业进行双链融合的一种模式。"
        "请为这个因子生成一个不超过8个词的、最能概括其核心语义的标签（每个标签凝练出这一因子所代表的企业双链融合的独特做法模式）。\n\n"
        "当前因子的代表性句子：\n{current_sentences}\n\n"
        "其他因子的代表性句子（用于区别对比）：\n{other_sentences}\n\n"
        "请确保生成的标签能够与其他因子明确区分，突出当前因子的独特性。\n"
        "请你输出标签名称，并且注意仅输出标签的名称，其他任何字段、理由、信息等均不要输出，请严格遵循我的指令要求。"
        "请输出："
    )
    
    # 改进的描述生成提示模板
    desc_prompt_template = (
        "企业的“产业链与创新链融合”是指：企业在产业链对科技创新需求的驱动下,将资源部署在促进产业技术进步领域,根据产业发展需求布局科技创新前沿,尤其是聚焦产业化前景明朗的前沿科技以及可满足当前产业需求的先进技术,带动创新链加速转化,加快实现双链融合” "
        "同时企业在创新链对技术转化需求的驱动下,产业化运营培育全新的产业领域以致推动产业链升级\n\n"
        "以下是一组围绕“产业链与创新链融合”企业做法、模式、路径相关核心主题的句子与信息，基于以下信息，请用1-2句话详细描述这个'文本结构因子'所代表的核心概念、"
        "议题或现象在“产业链与创新链融合”企业做法、模式、路径方面的具体体现。请确保描述突出该因子的独特性"
        "，突出因子与其他因子代表性句子的区别(但是不要直接说出区别)。\n\n"
        "当前因子标签：{label}\n"
        "当前因子的代表性句子：\n{current_sentences}\n\n"
        "其他因子信息（用于区别对比）：\n{other_factors_info}\n\n"
        "详细描述为（仅仅输出1-2句话的描述，其他任何字段、理由、信息等均不要输出，请严格遵循我的指令要求）："
        "请输出："
    )

    # 只处理非噪声簇
    for tsf_id, core_vector in tqdm(tsf_core_vectors.items(), desc="解释TSFs"):
        # 跳过噪声簇，因为已经在上面处理了
        if tsf_id == -1:
            continue
            
        # 获取当前TSF的代表性句子（使用更多句子，最多10个或全部）
        current_rep_sentences = get_representative_sentences_for_tsf(
            tsf_id, cluster_labels, sentences_with_ids_filtered, embeddings_filtered, 
            core_vector, top_n=20
        )
        
        if not current_rep_sentences:
            print(f"TSF {tsf_id} 没有代表性句子，跳过解释。")
            interpretations.append({
                'tsf_id': tsf_id, 
                'label': f'TSF_{tsf_id}_NoRepSentences', 
                'description': '未能提取代表性句子进行解释。',
                'representative_sentences_for_llm': []
            })
            continue

        # 准备当前因子的句子文本
        current_sentences_text = "\n".join([f"- {s}" for s in current_rep_sentences])
        
        # 准备其他因子的句子文本（用于对比），排除噪声簇
        other_sentences_parts = []
        for other_tsf_id, other_sentences in all_clusters_sentences.items():
            if other_tsf_id != tsf_id and other_tsf_id != -1 and other_sentences:  # 排除噪声簇
                other_sentences_text = "\n".join([f"  - {s}" for s in other_sentences[:2]])  # 只显示前2个句子避免prompt过长
                other_sentences_parts.append(f"因子{other_tsf_id}的代表句子：\n{other_sentences_text}")
        
        other_sentences_text = "\n\n".join(other_sentences_parts) if other_sentences_parts else "无其他因子"

        # 生成标签
        label_prompt = label_prompt_template.format(
            current_sentences=current_sentences_text,
            other_sentences=other_sentences_text
        )
        
        tsf_label = call_llm(label_prompt, llm_model, llm_client, max_tokens=30)
        if not tsf_label:
            tsf_label = f"TSF_{tsf_id}_LabelGenerationFailed"
        time.sleep(0.5)

        # 准备其他因子信息用于描述生成，排除噪声簇
        other_factors_info_parts = []
        for other_tsf_id, other_sentences in all_clusters_sentences.items():
            if other_tsf_id != tsf_id and other_tsf_id != -1 and other_sentences:  # 排除噪声簇
                # 简化的其他因子信息
                other_factors_info_parts.append(f"因子{other_tsf_id}: {other_sentences[0][:50]}...")
        
        other_factors_info = "\n".join(other_factors_info_parts) if other_factors_info_parts else "无其他因子"

        # 生成描述
        desc_prompt = desc_prompt_template.format(
            label=tsf_label, 
            current_sentences=current_sentences_text,
            other_factors_info=other_factors_info
        )
        
        tsf_description = call_llm(desc_prompt, llm_model, llm_client, max_tokens=120)
        if not tsf_description:
            tsf_description = "描述生成失败。"
        time.sleep(0.5)
        
        interpretations.append({
            'tsf_id': tsf_id,
            'label': tsf_label,
            'description': tsf_description,
            'representative_sentences_for_llm': current_rep_sentences,
            'num_representative_sentences': len(current_rep_sentences)
        })
        
        print(f"  TSF {tsf_id} 解释完成 (使用了 {len(current_rep_sentences)} 个代表性句子)")
        
    print("TSF解释完成。")
    return interpretations



# --- 主执行流程 ---
# --- 分步骤交互式版本 ---

def step1_load_data(input_file_path, text_column_name):
    """
    步骤1: 初始化客户端和加载数据
    返回: (df_input, dashscope_client)
    """
    print("=== 步骤1: 初始化客户端和加载数据 ===")
    
    # 初始化客户端
    dashscope_client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url=DASHSCOPE_BASE_URL)
    
    # 尝试多种编码格式读取CSV文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1', 'cp1252']
    df_input = None
    
    for encoding in encodings:
        try:
            df_input = pd.read_csv(input_file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码读取文件")
            break
        except UnicodeDecodeError:
            continue
        except Exception as e:
            if encoding == encodings[-1]:  # 最后一个编码也失败了
                raise e
            continue
    
    if df_input is None:
        raise ValueError(f"无法使用任何编码格式读取文件 {input_file_path}")
    
    # 验证面板数据必须列
    required_columns = ['id', 'year', text_column_name]
    missing_columns = [col for col in required_columns if col not in df_input.columns]
    if missing_columns:
        raise ValueError(f"面板数据缺少必需列: {missing_columns}。可用列: {df_input.columns.tolist()}")
    
    print(f"面板数据加载成功，共 {len(df_input)} 行数据")
    print(f"唯一企业数: {df_input['id'].nunique()}")
    print(f"年份范围: {df_input['year'].min()} - {df_input['year'].max()}")
    print(f"文本列: {text_column_name}")
    
    return df_input, dashscope_client

def step2_extract_sentences(df_input, text_column_name):
    """
    步骤2: 从面板数据中提取和拆分句子，保留企业-年份信息
    返回: all_sentences_with_ids (包含企业id和年份信息)
    """
    print("=== 步骤2: 从面板数据中提取和拆分句子 ===")
    
    all_sentences_with_ids = []
    for idx, row in tqdm(df_input.iterrows(), total=df_input.shape[0], desc="处理企业年报"):
        try:
            doc_text = row[text_column_name]
            company_id = row['id']
            year = row['year']
            
            sentences_in_doc = split_into_sentences(str(doc_text))
            for sent_idx, sentence_text in enumerate(sentences_in_doc):
                # 句子ID格式: company_id_year_sent_idx
                sentence_id = f"{company_id}_{year}_{sent_idx}"
                # 保存句子信息：(句子ID, 句子文本, 企业ID, 年份)
                all_sentences_with_ids.append((sentence_id, sentence_text, company_id, year))
        except Exception as e:
            print(f"处理企业 {row.get('id', 'unknown')} 年份 {row.get('year', 'unknown')} 时出错: {e}")
            continue
    
    print(f"总共从 {df_input['id'].nunique()} 家企业，{df_input['year'].nunique()} 个年份中提取到 {len(all_sentences_with_ids)} 个句子")
    return all_sentences_with_ids

def step3_keyword_screening(all_sentences_with_ids):
    """
    步骤3: 关键词初筛
    返回: screened_sentences_keywords
    """
    print("=== 步骤3: 关键词初筛 ===")
    
    screened_sentences_keywords = keyword_based_screening(
        all_sentences_with_ids, KEYWORDS_DICT, STOPWORDS
    )
    
    if not screened_sentences_keywords:
        print("警告: 关键词初筛后没有符合条件的句子")
    else:
        print(f"关键词初筛完成，筛选出 {len(screened_sentences_keywords)} 个句子")
    
    return screened_sentences_keywords

def step4_llm_screening(screened_sentences_keywords, dashscope_client):
    """
    步骤4: LLM深度语义筛选和增强提取一体化 - 增强的断点续传版本
    返回: screened_sentences_llm
    """
    print("=== 步骤4: LLM深度语义筛选和增强提取一体化 ===")
    
    if not screened_sentences_keywords:
        print("输入为空，跳过LLM筛选")
        return []
    
    # 检查是否有进度文件
    if os.path.exists(STEP4_PROGRESS_DIR):
        print("检测到上次未完成的进度...")
        
        # 显示进度信息
        if os.path.exists(STEP4_PROGRESS_FILE):
            try:
                with open(STEP4_PROGRESS_FILE, 'r', encoding='utf-8') as f:
                    progress_info = json.load(f)
                print(f"上次进度: {progress_info['timestamp']}, 已完成: {progress_info['total_completed']} 个任务")
            except:
                pass
        
        # 询问用户是否继续
        user_choice = input("是否从上次中断点继续？(y/n，默认y): ").strip().lower()
        if user_choice in ['n', 'no']:
            print("用户选择重新开始，清理旧进度...")
            _cleanup_progress_files()
    
    # 使用增强的LLM筛选函数
    screened_sentences_llm = llm_semantic_screening_and_extraction(
        screened_sentences_keywords, dashscope_client, LLM_MODEL_FOR_FILTERING, max_workers=200
    )
    
    if not screened_sentences_llm:
        print("警告: LLM语义筛选与提取后没有符合条件的句子")
    else:
        print(f"LLM语义筛选与提取完成，最终筛选出 {len(screened_sentences_llm)} 个句子")
    
    return screened_sentences_llm

def step5_generate_embeddings(screened_sentences_llm, dashscope_client):
    """
    步骤5: 生成句向量
    返回: (final_filtered_sentences_texts, embeddings_filtered)
    """
    print("=== 步骤5: 生成句向量 ===")
    
    if not screened_sentences_llm:
        print("输入为空，跳过向量生成")
        return [], np.array([])
    
    final_filtered_sentences_texts = [s[1] for s in screened_sentences_llm]
    
    embeddings_filtered = get_openai_embeddings(
        final_filtered_sentences_texts, dashscope_client, EMBEDDING_MODEL_OPENAI
    )
    
    # 新增：保存向量和文本
    save_embeddings_and_texts(embeddings_filtered, screened_sentences_llm)
       
    print(f"生成向量: {embeddings_filtered.shape}")
    
    if embeddings_filtered.shape[0] != len(final_filtered_sentences_texts):
        print(f"警告: 生成的向量数量 ({embeddings_filtered.shape[0]}) 与句子数量 ({len(final_filtered_sentences_texts)}) 不匹配")
    
    return final_filtered_sentences_texts, embeddings_filtered

def dbscan_clustering(embeddings, eps=0.5, min_samples=5):
    """
    使用DBSCAN进行聚类，自动参数可根据需要调整
    返回labels
    """
    print("\n[对比] DBSCAN聚类...")
    t0 = time.time()
    db = DBSCAN(eps=eps, min_samples=min_samples, metric='cosine', n_jobs=1)
    labels = db.fit_predict(embeddings)
    t1 = time.time()
    mask = labels != -1
    if np.sum(mask) > 1 and len(set(labels[mask])) > 1:
        ch_score = calinski_harabasz_score(embeddings[mask], labels[mask])
        silhouette_avg = silhouette_score(embeddings[mask], labels[mask])
        print(f"轮廓系数: {silhouette_avg:.4f}（越高越好，范围[-1,1]）")
        print(f"DBSCAN Calinski-Harabasz分数: {ch_score:.2f}")
        print(f"DBSCAN发现的簇个数: {len(set(labels[mask]))}")
    else:
        print("DBSCAN聚类簇数不足，未计算CH分数。")
    print(f"DBSCAN聚类耗时: {t1-t0:.2f}秒")
    return labels

def lsh_hierarchical_clustering(embeddings, n_clusters=None, lsh_dim=32):
    """
    先用LSH降维，再用层次聚类。n_clusters可自动估计或指定。
    """
    print("\n[对比] LSH+层次聚类...")
    t0 = time.time()
    # 简单LSH降维（随机投影）
    np.random.seed(42)
    rand_matrix = np.random.randn(embeddings.shape[1], lsh_dim)
    lsh_emb = np.dot(embeddings, rand_matrix)
    # 自动估计簇数（这里简单设为8或样本数/20）
    if n_clusters is None:
        n_clusters = min(8, max(2, embeddings.shape[0] // 20))
    model = AgglomerativeClustering(n_clusters=n_clusters, linkage='ward')
    labels = model.fit_predict(lsh_emb)
    t1 = time.time()
    # 只用原始向量embeddings计算CH分数
    if len(set(labels)) > 1:
        ch_score = calinski_harabasz_score(embeddings, labels)
        silhouette_avg = silhouette_score(embeddings, labels)
        print(f"轮廓系数: {silhouette_avg:.4f}（越高越好，范围[-1,1]）")
        print(f"LSH+层次聚类 Calinski-Harabasz分数（原始空间）: {ch_score:.2f}")
    else:
        print("LSH+层次聚类簇数不足，未计算CH分数。")
    print(f"LSH+层次聚类耗时: {t1-t0:.2f}秒")
    return labels

def kmeans_clustering(embeddings, n_clusters=8):
    """
    使用KMeans进行聚类，n_clusters可自动估计或指定。
    返回labels
    """
    print("\n[对比] KMeans聚类...")
    t0 = time.time()
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init='auto')
    labels = kmeans.fit_predict(embeddings)
    t1 = time.time()
    if len(set(labels)) > 1:
        ch_score = calinski_harabasz_score(embeddings, labels)
        silhouette_avg = silhouette_score(embeddings, labels)
        print(f"轮廓系数: {silhouette_avg:.4f}（越高越好，范围[-1,1]）")
        print(f"KMeans Calinski-Harabasz分数: {ch_score:.2f}")
    else:
        print("KMeans聚类簇数不足，未计算CH分数。")
    print(f"KMeans聚类耗时: {t1-t0:.2f}秒")
    return labels


def step6_clustering(embeddings_filtered):
    """
    步骤6: S-DCESCD语义社区发现
    返回: cluster_labels
    参数调整
    """
    print("=== 步骤6: S-DCESCD语义社区发现 ===")
    if embeddings_filtered.shape[0] == 0:
        print("输入向量为空，跳过聚类")
        return np.array([])
    t0 = time.time()
    cluster_labels = s_dcescd_clustering(
        embeddings_filtered, 
        min_cluster_size=10,
        k_neighbors=3,
        eps_percentile=0.3,
        similarity_threshold=0.75,
        merge_threshold=0.85
    )
    t1 = time.time()
    unique_labels, counts = np.unique(cluster_labels, return_counts=True)
    print(f"聚类完成，标签分布: {dict(zip(unique_labels, counts))}")
    try:
        mask = cluster_labels
        n_clusters = len(set(cluster_labels[mask]))
        if np.sum(mask) > 1 and n_clusters > 1:
            ch_score = calinski_harabasz_score(embeddings_filtered[mask], cluster_labels[mask])
            silhouette_avg = silhouette_score(embeddings_filtered[mask], cluster_labels[mask])
            print(f"轮廓系数: {silhouette_avg:.4f}（越高越好，范围[-1,1]）")
            print(f"Calinski-Harabasz分数: {ch_score:.2f}（越高越好）")
        else:
            print("聚类簇数不足，未计算聚类评价分数。")
    except Exception as e:
        print(f"聚类评价分数计算失败: {e}")
    print(f"S-DCESCD聚类耗时: {t1-t0:.2f}秒")
    return cluster_labels

# --- 步骤7: TSFs构建 ---
def step7_build_tsfs(embeddings_filtered, cluster_labels):
    """
    步骤7: TSFs构建与量化
    返回: (tsf_core_vectors, tsf_loadings_data)
    """
    print("=== 步骤7: TSFs构建与量化 ===")
    
    if embeddings_filtered.shape[0] == 0:
        print("输入向量为空，跳过TSFs构建")
        return {}, []
    
    # 生成TSF核心向量
    tsf_core_vectors = generate_tsf_core_vectors(embeddings_filtered, cluster_labels)
    
    # 计算TSF载荷
    tsf_loadings_data = calculate_tsf_loadings(embeddings_filtered, tsf_core_vectors)
    
    print(f"TSFs构建完成，共生成 {len(tsf_core_vectors)} 个TSF核心向量")
    
    return tsf_core_vectors, tsf_loadings_data

# --- 步骤8: TSF解释 ---
def step8_interpret_tsfs(tsf_core_vectors, cluster_labels, screened_sentences_llm, 
                        embeddings_filtered, dashscope_client):
    """
    步骤8: LLM辅助的TSF解释
    返回: tsf_interpretations_list
    """
    print("=== 步骤8: LLM辅助的TSF解释 ===")
    
    if not tsf_core_vectors:
        print("没有TSF核心向量，跳过解释")
        return []
    
    tsf_interpretations_list = interpret_tsfs_with_llm(
        tsf_core_vectors, 
        cluster_labels, 
        screened_sentences_llm,
        embeddings_filtered, 
        dashscope_client, 
        LLM_MODEL_FOR_INTERPRETATION
    )
    
    print(f"TSF解释完成，共解释 {len(tsf_interpretations_list)} 个TSF")
    
    return tsf_interpretations_list

# --- 步骤9: 结果整合与输出 ---
def step9_save_results(screened_sentences_llm, cluster_labels, tsf_loadings_data, 
                      tsf_interpretations_list):
    """
    步骤9: 结果整合与输出
    返回: (df_results, df_interpretations)
    """
    print("=== 步骤9: 结果整合与输出 ===")
    
    if not screened_sentences_llm:
        print("没有筛选结果，创建空的结果文件")
        df_results = pd.DataFrame()
        df_interpretations = pd.DataFrame()
    else:
        # 构建主结果DataFrame
        results_list = []
        for i in range(len(screened_sentences_llm)):
            sent_global_id, sent_text = screened_sentences_llm[i]
            row_data = {
                'sentence_global_id': sent_global_id,
                'sentence_text': sent_text,
                'cluster_id': cluster_labels[i] if i < len(cluster_labels) else -1
            }
            
            # 添加TSF载荷信息
            if (i < len(tsf_loadings_data) and 
                tsf_loadings_data[i]['sentence_idx_in_filtered_set'] == i):
                row_data.update({k: v for k, v in tsf_loadings_data[i].items() 
                               if k != 'sentence_idx_in_filtered_set'})
            
            results_list.append(row_data)
        
        df_results = pd.DataFrame(results_list)
        
        # 合并TSF解释
        df_interpretations = pd.DataFrame(tsf_interpretations_list)
        if not df_interpretations.empty:
            df_interpretations_renamed = df_interpretations.rename(columns={
                'tsf_id': 'cluster_id', 
                'label': 'tsf_label', 
                'description': 'tsf_description'
            })
            df_results = pd.merge(
                df_results, 
                df_interpretations_renamed[['cluster_id', 'tsf_label', 'tsf_description']], 
                on='cluster_id', 
                how='left'
            )
    
    # 保存文件
    df_results.to_csv(OUTPUT_RESULTS_CSV_PATH, index=False, encoding='utf-8-sig')
    print(f"主要分析结果已保存到: {OUTPUT_RESULTS_CSV_PATH}")
    
    if not df_interpretations.empty:
        df_interpretations.to_csv(OUTPUT_TSF_INTERPRETATIONS_CSV_PATH, index=False, encoding='utf-8-sig')
        print(f"TSF解释详情已保存到: {OUTPUT_TSF_INTERPRETATIONS_CSV_PATH}")
    
    return df_results, df_interpretations

def step9_calculate_company_tsf_scores(screened_sentences_llm, embeddings_filtered, 
                                      tsf_core_vectors, cluster_labels):
    """
    步骤9: 计算企业-年份级别的TSF聚合载荷得分
    使用聚合载荷思想：先计算文档整体向量表示，然后计算与TSF核心向量的余弦相似度
    返回: df_company_scores
    """
    print("=== 步骤9: 计算企业-年份级别TSF聚合载荷得分 ===")
    
    if not screened_sentences_llm or len(embeddings_filtered) == 0 or not tsf_core_vectors:
        print("输入数据为空，跳过企业TSF得分计算")
        return pd.DataFrame()
    
    # 1. 构建企业-年份到句子的映射
    company_year_sentences = {}
    for i, (sent_id, sent_text, company_id, year) in enumerate(screened_sentences_llm):
        # 提取企业ID和年份信息
        company_year_key = f"{company_id}_{year}"
        
        if company_year_key not in company_year_sentences:
            company_year_sentences[company_year_key] = {
                'company_id': company_id,
                'year': year,
                'sentence_indices': [],
                'sentence_texts': []
            }
        
        company_year_sentences[company_year_key]['sentence_indices'].append(i)
        company_year_sentences[company_year_key]['sentence_texts'].append(sent_text)
    
    print(f"发现 {len(company_year_sentences)} 个企业-年份组合")
    
    # 2. 计算每个企业-年份的文档整体向量表示
    company_scores = []
    tsf_ids = sorted(tsf_core_vectors.keys())
    
    for company_year_key, data in tqdm(company_year_sentences.items(), desc="计算企业TSF得分"):
        company_id = data['company_id']
        year = data['year']
        sentence_indices = data['sentence_indices']
        
        # 获取该企业-年份的所有句子向量
        company_embeddings = embeddings_filtered[sentence_indices]
        
        # 计算文档整体向量表示（句向量平均）
        if len(company_embeddings) > 0:
            document_vector = np.mean(company_embeddings, axis=0)
        else:
            print(f"警告: 企业 {company_id} 年份 {year} 没有有效的句向量")
            continue
        
        # 构建得分记录
        score_record = {
            'company_id': company_id,
            'year': year,
            'sentence_count': len(sentence_indices),
            'company_year_key': company_year_key
        }
        
        # 3. 计算与每个TSF核心向量的余弦相似度
        for tsf_id in tsf_ids:
            tsf_core_vector = tsf_core_vectors[tsf_id]
            
            # 计算聚合载荷：L_mk = cos(d_m, f_k)
            similarity = cosine_similarity(
                document_vector.reshape(1, -1), 
                tsf_core_vector.reshape(1, -1)
            )[0, 0]
            
            score_record[f'TSF_{tsf_id}_score'] = similarity
        
        company_scores.append(score_record)
    
    # 4. 转换为DataFrame
    df_company_scores = pd.DataFrame(company_scores)
    
    if not df_company_scores.empty:
        # 添加统计信息
        print(f"企业TSF得分计算完成:")
        print(f"  - 企业数量: {df_company_scores['company_id'].nunique()}")
        print(f"  - 年份数量: {df_company_scores['year'].nunique()}")
        print(f"  - 企业-年份观测数: {len(df_company_scores)}")
        print(f"  - TSF维度数: {len(tsf_ids)}")
        
        # 显示得分统计
        score_columns = [col for col in df_company_scores.columns if col.startswith('TSF_') and col.endswith('_score')]
        if score_columns:
            print("  TSF得分统计:")
            for col in score_columns:
                mean_score = df_company_scores[col].mean()
                std_score = df_company_scores[col].std()
                print(f"    {col}: 均值={mean_score:.4f}, 标准差={std_score:.4f}")
    

    
    return df_company_scores

def step10_save_all_results(screened_sentences_llm, cluster_labels, tsf_loadings_data, 
                           tsf_interpretations_list, df_company_scores):
    """
    步骤10: 保存所有结果（句子级别和企业级别）
    返回: (df_results, df_interpretations, df_company_scores)
    """
    print("=== 步骤10: 结果整合与输出 ===")
    
    # 保存句子级别的结果（原有逻辑）
    if not screened_sentences_llm:
        print("没有筛选结果，创建空的结果文件")
        df_results = pd.DataFrame()
        df_interpretations = pd.DataFrame()
    else:
        # 构建主结果DataFrame
        results_list = []
        for i in range(len(screened_sentences_llm)):
            sent_global_id, sent_text, company_id, year = screened_sentences_llm[i]
            row_data = {
                'sentence_global_id': sent_global_id,
                'sentence_text': sent_text,
                'company_id': company_id,
                'year': year,
                'cluster_id': cluster_labels[i] if i < len(cluster_labels) else -1
            }
            
            # 添加TSF载荷信息
            if (i < len(tsf_loadings_data) and 
                tsf_loadings_data[i]['sentence_idx_in_filtered_set'] == i):
                row_data.update({k: v for k, v in tsf_loadings_data[i].items() 
                               if k != 'sentence_idx_in_filtered_set'})
            
            results_list.append(row_data)
        
        df_results = pd.DataFrame(results_list)
        
        # 合并TSF解释
        df_interpretations = pd.DataFrame(tsf_interpretations_list)
        if not df_interpretations.empty:
            df_interpretations_renamed = df_interpretations.rename(columns={
                'tsf_id': 'cluster_id', 
                'label': 'tsf_label', 
                'description': 'tsf_description'
            })
            df_results = pd.merge(
                df_results, 
                df_interpretations_renamed[['cluster_id', 'tsf_label', 'tsf_description']], 
                on='cluster_id', 
                how='left'
            )
    
    # 保存句子级结果文件
    df_results.to_csv(OUTPUT_RESULTS_CSV_PATH, index=False, encoding='utf-8-sig')
    print(f"句子级分析结果已保存到: {OUTPUT_RESULTS_CSV_PATH}")
    
    if not df_interpretations.empty:
        df_interpretations.to_csv(OUTPUT_TSF_INTERPRETATIONS_CSV_PATH, index=False, encoding='utf-8-sig')
        print(f"TSF解释详情已保存到: {OUTPUT_TSF_INTERPRETATIONS_CSV_PATH}")
    
    # 保存企业级TSF得分结果
    if not df_company_scores.empty:
        company_scores_path = 'company_tsf_scores.csv'
        df_company_scores.to_csv(company_scores_path, index=False, encoding='utf-8-sig')
        print(f"企业级TSF得分已保存到: {company_scores_path}")
        
        # 展示企业得分结果的前几行
        print("\n企业TSF得分结果预览:")
        print(df_company_scores.head().to_string())
    else:
        print("企业TSF得分为空，未保存文件")
    
    return df_results, df_interpretations, df_company_scores

def _cleanup_progress_files():
    """清理进度文件"""
    try:
        import shutil
        if os.path.exists(STEP4_PROGRESS_DIR):
            shutil.rmtree(STEP4_PROGRESS_DIR)
    except Exception as e:
        print(f"清理进度文件失败: {e}")
























# 步骤1: 加载数据
df_input, dashscope_client = step1_load_data(INPUT_CSV_FILE, TEXT_COLUMN)

# 步骤2: 提取句子
all_sentences_with_ids = step2_extract_sentences(df_input, TEXT_COLUMN)

# 步骤3: 关键词筛选
screened_sentences_keywords = step3_keyword_screening(all_sentences_with_ids)


# 3.5 保存
saved = step35_save_intermediate_choice(screened_sentences_keywords)

# 检查是否存在之前保存的中间结果，询问用户是否使用
if os.path.exists(STEP35_INFO_FILE) and not saved:
    try:
        with open(STEP35_INFO_FILE, 'r', encoding='utf-8') as f:
            step35_info = json.load(f)
        
        print(f"\n检测到之前保存的步骤3.5中间结果:")
        print(f"保存时间: {step35_info.get('timestamp', '未知')}")
        print(f"筛选句子数: {step35_info.get('filtered_count', 0)} 条")
                
    except Exception as e:
        print(f"读取已保存结果时出错: {e}，将使用当前结果继续")



# 直接加载步骤3.5保存的结果
screened_sentences_keywords, info = load_step35_intermediate_results()
# 验证加载的数据格式
if screened_sentences_keywords is None:
    print("❌ 未能加载步骤3.5的结果，请先运行完整流程")
    exit()

print(f"✓ 成功加载步骤3.5结果: {len(screened_sentences_keywords)} 个句子")

# 验证数据格式是否正确（应该是4元组：句子ID, 句子文本, 企业ID, 年份）
if screened_sentences_keywords and len(screened_sentences_keywords) > 0:
    first_item = screened_sentences_keywords[0]
    if not isinstance(first_item, (list, tuple)) or len(first_item) != 4:
        print(f"❌ 数据格式错误，期望4元组，实际: {type(first_item)}, 长度: {len(first_item) if hasattr(first_item, '__len__') else 'N/A'}")
        print(f"第一个项目示例: {first_item}")
        exit()
    else:
        print(f"✓ 数据格式验证通过，示例: {first_item}")

# 步骤4: LLM筛选
screened_sentences_llm = step4_llm_screening(screened_sentences_keywords, dashscope_client)



# ==== 跳过第四步，直接读取llm_results.json ====

llm_results_path = os.path.join(os.path.dirname(__file__), "step4_progress", "llm_results.json")
if os.path.exists(llm_results_path):
    with open(llm_results_path, "r", encoding="utf-8") as f:
        llm_data = json.load(f)
    # 兼容格式：[(id, sentence, company_id, year), ...]
    screened_sentences_llm = [
        (item.get("id"), item.get("sentence"), item.get("company_id"), item.get("year"))
        for item in llm_data
        if item.get("id") and item.get("sentence") is not None
    ]
    print(f"已直接加载llm_results.json，共{len(screened_sentences_llm)}条。")
else:
    print("未找到llm_results.json，将使用原有screened_sentences_llm。")




# 步骤5: 生成向量
final_filtered_sentences_texts, embeddings_filtered = step5_generate_embeddings(
    screened_sentences_llm, dashscope_client
)

# 加载本地保存的向量和文本（如果存在）
final_filtered_sentences_texts, embeddings_filtered, screened_sentences_llm = load_embeddings_and_texts()

# 步骤6: 聚类
cluster_labels = step6_clustering(embeddings_filtered)

# 对比聚类效果
# DBSCAN
dbscan_labels = dbscan_clustering(embeddings_filtered, eps=0.05, min_samples=10)
# LSH+层次聚类
lsh_hier_labels = lsh_hierarchical_clustering(embeddings_filtered, n_clusters=4, lsh_dim=256)
# KMeans聚类
kmeans_labels = kmeans_clustering(embeddings_filtered, n_clusters=4)

# 步骤7: TSFs构建
tsf_core_vectors, tsf_loadings_data = step7_build_tsfs(embeddings_filtered, cluster_labels)

# 步骤8: TSF解释
tsf_interpretations_list = step8_interpret_tsfs(
    tsf_core_vectors, cluster_labels, screened_sentences_llm, 
    embeddings_filtered, dashscope_client
)

# 步骤9: 计算企业级TSF得分
df_company_scores = step9_calculate_company_tsf_scores(
    screened_sentences_llm, embeddings_filtered, tsf_core_vectors, cluster_labels
)

# 步骤10: 保存所有结果（句子级别和企业级别）
df_results, df_interpretations, df_company_scores = step10_save_all_results(
    screened_sentences_llm, cluster_labels, tsf_loadings_data, tsf_interpretations_list, df_company_scores
)

print("=" * 50)
print("完整流程执行完毕！")
print("=" * 50)


# 创建embeddings_filtered的5倍扩充版本
embeddings_filtered_1 = np.tile(embeddings_filtered, (3, 1))
cluster_labels = step6_clustering(embeddings_filtered_1)

# 对比聚类效果
# DBSCAN
dbscan_labels = dbscan_clustering(embeddings_filtered_1, eps=0.2, min_samples=2)
# LSH+层次聚类
lsh_hier_labels = lsh_hierarchical_clustering(embeddings_filtered_1, n_clusters=4, lsh_dim=256)





